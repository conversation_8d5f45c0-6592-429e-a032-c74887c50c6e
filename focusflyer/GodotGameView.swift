//
//  GodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct GodotGameView: View {
    @State private var godotApp: GodotApp?
    @State private var errorMessage: String?
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        NavigationView {
            ZStack {
                if let godotApp = godotApp {
                    // Godot game view
                    GodotAppView()
                        .environment(\.godotApp, godotApp)
                        .ignoresSafeArea()
                } else if let errorMessage = errorMessage {
                    // Error view
                    VStack(spacing: 20) {
                        Image(systemName: "gamecontroller")
                            .font(.system(size: 50))
                            .foregroundColor(.blue)

                        Text("Godot Game")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text(errorMessage)
                            .font(.body)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)

                        VStack(spacing: 10) {
                            Text("To add a Godot game:")
                                .font(.headline)

                            VStack(alignment: .leading, spacing: 5) {
                                Text("1. Create a project in Godot Editor")
                                Text("2. Export as iOS .pck file")
                                Text("3. Replace test2.pck in the project")
                            }
                            .font(.caption)
                            .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)

                        Button("Try Again") {
                            self.errorMessage = nil
                            loadGodotGame()
                        }
                        .buttonStyle(.bordered)
                    }
                    .padding()
                } else {
                    // Loading view
                    VStack(spacing: 20) {
                        ProgressView()
                            .scaleEffect(1.5)

                        Text("Loading Godot Game...")
                            .font(.title2)
                    }
                }

                // Close button overlay
                VStack {
                    HStack {
                        Spacer()
                        Button(action: {
                            isPresented = false
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title)
                                .foregroundColor(.white)
                                .background(Color.black.opacity(0.6))
                                .clipShape(Circle())
                        }
                        .padding()
                    }
                    Spacer()
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            if godotApp == nil {
                loadGodotGame()
            }
        }
        .onDisappear {
            // Clean up Godot when view disappears
            cleanupGodot()
        }
    }

    private func loadGodotGame() {
        print("Loading Godot game...")

        // Check if the pck file exists
        guard let pckPath = Bundle.main.path(forResource: "test2", ofType: "pck") else {
            print("Game file 'test2.pck' not found in app bundle")
            // Try to create a minimal Godot app without a pck file
            loadMinimalGodotApp()
            return
        }

        print("Found pck file at: \(pckPath)")

        // Try to initialize the Godot app
        let app = GodotApp(packFile: "test2.pck")
        self.godotApp = app
        print("Successfully loaded Godot game")
    }

    private func loadMinimalGodotApp() {
        print("Loading minimal Godot app...")

        // Show error message instead of trying to create empty Godot app
        errorMessage = "No valid Godot game file found. Please add a valid .pck file to the project."
    }

    private func cleanupGodot() {
        print("Cleaning up Godot...")
        // The GodotApp will handle cleanup automatically when deallocated
        // But we can explicitly set it to nil to trigger cleanup
        godotApp = nil
    }
}

#Preview {
    GodotGameView(isPresented: .constant(true))
}
