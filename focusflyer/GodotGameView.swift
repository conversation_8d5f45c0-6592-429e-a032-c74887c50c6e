//
//  GodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

enum GodotState {
    case idle
    case initializing
    case running
    case stopping
    case error(String)
}

struct GodotGameView: View {
    @State private var godotApp: GodotApp?
    @State private var godotState: GodotState = .idle
    @State private var errorMessage: String?
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        NavigationView {
            ZStack {
                switch godotState {
                case .running:
                    if let godotApp = godotApp {
                        // Godot game view
                        GodotAppView()
                            .environment(\.godotApp, godotApp)
                            .ignoresSafeArea()
                    }
                case .error(let message):
                    // Error view
                    VStack(spacing: 20) {
                        Image(systemName: "gamecontroller")
                            .font(.system(size: 50))
                            .foregroundColor(.red)

                        Text("Godot Game Error")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text(message)
                            .font(.body)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)

                        VStack(spacing: 10) {
                            Text("To add a Godot game:")
                                .font(.headline)

                            VStack(alignment: .leading, spacing: 5) {
                                Text("1. Create a project in Godot Editor")
                                Text("2. Export as iOS .pck file")
                                Text("3. Replace test2.pck in the project")
                            }
                            .font(.caption)
                            .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(10)

                        Button("Try Again") {
                            Task {
                                await loadGodotGame()
                            }
                        }
                        .buttonStyle(.bordered)
                    }
                    .padding()
                default:
                    // Loading view
                    VStack(spacing: 20) {
                        ProgressView()
                            .scaleEffect(1.5)

                        Text(loadingText)
                            .font(.title2)
                    }
                }

                // Close button overlay
                VStack {
                    HStack {
                        Spacer()
                        Button(action: {
                            Task {
                                await closeGodotGame()
                                isPresented = false
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title)
                                .foregroundColor(.white)
                                .background(Color.black.opacity(0.6))
                                .clipShape(Circle())
                        }
                        .padding()
                    }
                    Spacer()
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            if godotState == .idle {
                Task {
                    await loadGodotGame()
                }
            }
        }
        .onDisappear {
            // Clean up Godot when view disappears
            Task {
                await cleanupGodot()
            }
        }
    }

    private var loadingText: String {
        switch godotState {
        case .initializing:
            return "Initializing Godot..."
        case .stopping:
            return "Stopping Godot..."
        default:
            return "Loading Godot Game..."
        }
    }

    @MainActor
    private func loadGodotGame() async {
        // Prevent multiple simultaneous initializations
        guard godotState == .idle || godotState == .error("") else {
            print("Godot is already initializing or running")
            return
        }

        godotState = .initializing
        print("Loading Godot game...")

        do {
            // Check if the pck file exists
            guard let pckPath = Bundle.main.path(forResource: "test2", ofType: "pck") else {
                print("Game file 'test2.pck' not found in app bundle")
                throw GodotError.pckFileNotFound
            }

            print("Found pck file at: \(pckPath)")

            // Initialize Godot on a background thread to avoid blocking UI
            let app = try await withCheckedThrowingContinuation { continuation in
                Task.detached {
                    do {
                        print("Initializing GodotApp...")
                        let godotApp = GodotApp(packFile: "test2.pck")

                        // Give Godot time to initialize properly
                        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

                        print("Starting Godot engine...")
                        // Note: Depending on SwiftGodotKit version, you might need to call start() or run()
                        // godotApp.start() // Uncomment if available

                        continuation.resume(returning: godotApp)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }

            await MainActor.run {
                self.godotApp = app
                self.godotState = .running
                print("Successfully loaded and started Godot game")
            }

        } catch {
            await MainActor.run {
                let errorMsg = "Failed to load Godot game: \(error.localizedDescription)"
                print(errorMsg)
                self.godotState = .error(errorMsg)
                self.godotApp = nil
            }
        }
    }

    @MainActor
    private func closeGodotGame() async {
        await cleanupGodot()
    }

    @MainActor
    private func cleanupGodot() async {
        guard godotState != .idle && godotState != .stopping else {
            return
        }

        godotState = .stopping
        print("Cleaning up Godot...")

        if let app = godotApp {
            // Perform cleanup on background thread
            await withCheckedContinuation { continuation in
                Task.detached {
                    // Note: Depending on SwiftGodotKit version, you might need to call stop() or shutdown()
                    // app.stop() // Uncomment if available

                    // Give time for proper cleanup
                    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

                    continuation.resume()
                }
            }
        }

        godotApp = nil
        godotState = .idle
        print("Godot cleanup completed")
    }
}

enum GodotError: LocalizedError {
    case pckFileNotFound
    case initializationFailed(String)

    var errorDescription: String? {
        switch self {
        case .pckFileNotFound:
            return "No valid Godot game file found. Please add a valid .pck file to the project."
        case .initializationFailed(let message):
            return "Failed to initialize Godot: \(message)"
        }
    }
}

#Preview {
    GodotGameView(isPresented: .constant(true))
}
