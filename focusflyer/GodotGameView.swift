//
//  GodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct GodotGameView: View {
    @State private var godotApp: GodotApp?
    @State private var errorMessage: String?
    @State private var isLoading = true
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let godotApp = godotApp {
                // Godot game view - following the demo pattern
                VStack {
                    GodotAppView()
                        .ignoresSafeArea()
                }
                .environment(\.godotApp, godotApp)
            } else if let errorMessage = errorMessage {
                // Error view
                VStack(spacing: 20) {
                    Image(systemName: "gamecontroller")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Godot Game Error")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    VStack(spacing: 10) {
                        Text("To add a Godot game:")
                            .font(.headline)

                        VStack(alignment: .leading, spacing: 5) {
                            Text("1. Create a project in Godot Editor")
                            Text("2. Export as iOS .pck file")
                            Text("3. Replace test2.pck in the project")
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)

                    Button("Try Again") {
                        loadGodotGame()
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
            } else if isLoading {
                // Loading view
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)

                    Text("Loading Godot Game...")
                        .font(.title2)
                }
            }

            // Close button overlay
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            if godotApp == nil && !isLoading {
                loadGodotGame()
            }
        }
    }

    private func loadGodotGame() {
        print("Loading Godot game...")
        isLoading = true
        errorMessage = nil

        // Check if the pck file exists
        guard Bundle.main.path(forResource: "test2", ofType: "pck") != nil else {
            print("Game file 'test2.pck' not found in app bundle")
            errorMessage = "No valid Godot game file found. Please add a valid .pck file to the project."
            isLoading = false
            return
        }

        print("Found pck file, initializing GodotApp...")

        // Following the demo pattern - simple initialization
        do {
            let app = GodotApp(packFile: "test2.pck")
            self.godotApp = app
            self.isLoading = false
            print("Successfully loaded Godot game")
        } catch {
            print("Failed to initialize GodotApp: \(error)")
            errorMessage = "Failed to load Godot game: \(error.localizedDescription)"
            isLoading = false
        }
    }
}

#Preview {
    GodotGameView(isPresented: .constant(true))
}
