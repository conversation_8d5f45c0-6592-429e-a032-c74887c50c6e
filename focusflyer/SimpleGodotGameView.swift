//
//  SimpleGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

// Global Godot app instance to avoid reinitialization issues
private var globalGodotApp: GodotApp? = nil
private var isGodotInitialized = false

struct SimpleGodotGameView: View {
    @State private var app: GodotApp?
    @State private var errorMessage: String?
    @State private var isLoading = false
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let app = app {
                VStack {
                    Text("Godot Game is below:")
                        .foregroundColor(.white)
                        .padding(.top, 50)

                    GodotAppView()
                        .padding()
                        .background(Color.black)
                }
                .environment(\.godotApp, app)
            } else if let errorMessage = errorMessage {
                VStack(spacing: 20) {
                    Image(systemName: "gamecontroller")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Godot Setup Issue")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Button("Try Again") {
                        loadGame()
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
            } else if isLoading {
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("Loading Godot Game...")
                        .font(.title2)
                }
            } else {
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("Initializing...")
                        .font(.title2)
                }
            }

            // Close button
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        cleanupAndClose()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            loadGame()
        }
    }

    private func loadGame() {
        print("Loading Godot game...")
        isLoading = true
        errorMessage = nil

        // Check if we already have a global Godot instance
        if let existingApp = globalGodotApp, isGodotInitialized {
            print("Reusing existing Godot instance")
            self.app = existingApp
            self.isLoading = false
            return
        }

        // Check if PCK file exists
        guard let pckPath = Bundle.main.path(forResource: "test2", ofType: "pck") else {
            errorMessage = "PCK file 'test2.pck' not found in app bundle"
            isLoading = false
            return
        }

        let fileSize = getFileSize(path: pckPath)
        print("Found PCK file: \(pckPath), size: \(fileSize) bytes")

        if fileSize < 100 {
            errorMessage = """
            PCK file is too small (\(fileSize) bytes).

            Please create a proper Godot project:
            1. Open Godot Editor
            2. Create a new project with some content
            3. Export as PCK file for iOS
            4. Replace test2.pck in the app bundle
            """
            isLoading = false
            return
        }

        // Only create GodotApp once globally
        if globalGodotApp == nil {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                do {
                    print("Creating global GodotApp instance...")
                    let godotApp = GodotApp(packFile: "test2.pck")
                    globalGodotApp = godotApp

                    // Give Godot time to fully initialize
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        isGodotInitialized = true
                        self.app = godotApp
                        self.isLoading = false
                        print("Successfully created and initialized global GodotApp")
                    }
                } catch {
                    print("Failed to create GodotApp: \(error)")
                    self.errorMessage = "Failed to create GodotApp: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }

    private func cleanupAndClose() {
        print("Cleaning up Godot before closing...")

        // Proper cleanup sequence
        if let _ = app {
            app = nil

            // Give time for Godot to cleanup properly
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // Reset all state
                self.hasInitialized = false
                self.isLoading = false
                self.errorMessage = nil

                // Close the view
                self.isPresented = false
            }
        } else {
            isPresented = false
        }
    }

    private func getFileSize(path: String) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
}

#Preview {
    SimpleGodotGameView(isPresented: .constant(true))
}
