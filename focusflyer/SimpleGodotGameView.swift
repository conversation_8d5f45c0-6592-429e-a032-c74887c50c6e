//
//  SimpleGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct SimpleGodotGameView: View {
    // 引擎初始化一次，PCK加载由用户控制
    @State private var godotApp: GodotApp?
    @State private var errorMessage: String?
    @State private var isEngineReady = false
    @State private var isPCKLoaded = false
    @State private var showControls = false
    @Binding var isPresented: Bool

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let godotApp = godotApp, isPCKLoaded {
                // Godot游戏视图
                VStack {
                    if showControls {
                        HStack {
                            Text("Godot Game Running")
                                .foregroundColor(.white)
                                .font(.caption)
                            Spacer()
                            Button("Hide Controls") {
                                showControls = false
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                        .padding(.horizontal)
                        .padding(.top, 50)
                    }

                    GodotAppView()
                        .background(Color.black)
                        .onTapGesture(count: 2) {
                            showControls.toggle()
                        }
                }
                .environment(\.godotApp, godotApp)
            } else if let errorMessage = errorMessage {
                // 错误视图
                VStack(spacing: 20) {
                    Image(systemName: "gamecontroller")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Godot Setup Issue")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Button("Retry") {
                        initializeEngine()
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
            } else if isEngineReady && !isPCKLoaded {
                // 引擎就绪，等待加载PCK
                VStack(spacing: 20) {
                    Image(systemName: "gamecontroller")
                        .font(.system(size: 50))
                        .foregroundColor(.green)

                    Text("Godot Engine Ready")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("Engine initialized successfully")
                        .font(.body)
                        .foregroundColor(.secondary)

                    Button("Load Game") {
                        loadPCKFile()
                    }
                    .buttonStyle(.borderedProminent)
                    .font(.title3)
                }
                .padding()
            } else {
                // 初始化中
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("Initializing Godot Engine...")
                        .font(.title2)
                }
            }

            // Close button
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        closeView()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            initializeEngine()
        }
    }

    // MARK: - 引擎初始化（只执行一次）
    private func initializeEngine() {
        guard godotApp == nil else {
            print("Godot engine already initialized")
            isEngineReady = true
            return
        }

        print("Initializing Godot engine...")
        errorMessage = nil

        // 创建空的Godot引擎实例（不加载PCK）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            do {
                print("Creating Godot engine instance...")
                // 创建一个空的GodotApp，引擎会初始化但不加载任何内容
                let app = GodotApp()
                self.godotApp = app

                // 给引擎时间初始化
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.isEngineReady = true
                    print("Godot engine initialized successfully")
                }
            } catch {
                print("Failed to initialize Godot engine: \(error)")
                self.errorMessage = "Failed to initialize Godot engine: \(error.localizedDescription)"
            }
        }
    }

    // MARK: - PCK文件加载（用户控制）
    private func loadPCKFile() {
        guard let godotApp = godotApp, isEngineReady else {
            errorMessage = "Godot engine not ready"
            return
        }

        print("Loading PCK file...")

        // 检查PCK文件
        guard let pckPath = Bundle.main.path(forResource: "test2", ofType: "pck") else {
            errorMessage = "PCK file 'test2.pck' not found in app bundle"
            return
        }

        let fileSize = getFileSize(path: pckPath)
        print("Found PCK file: \(pckPath), size: \(fileSize) bytes")

        if fileSize < 100 {
            errorMessage = """
            PCK file is too small (\(fileSize) bytes).

            Please create a proper Godot project:
            1. Open Godot Editor
            2. Create a new project with some content
            3. Export as PCK file for iOS
            4. Replace test2.pck in the app bundle
            """
            return
        }

        // 加载PCK文件到已初始化的引擎
        do {
            print("Loading PCK into Godot engine...")
            // 这里需要调用Godot的PCK加载API
            // 注意：这可能需要SwiftGodotKit的特定API
            try godotApp.loadPack(path: pckPath)

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.isPCKLoaded = true
                print("PCK file loaded successfully")
            }
        } catch {
            print("Failed to load PCK file: \(error)")
            errorMessage = "Failed to load PCK file: \(error.localizedDescription)"
        }
    }

    // MARK: - 清理和关闭
    private func closeView() {
        print("Closing Godot view...")

        // 不清理引擎，只是隐藏视图
        // 引擎保持运行，下次可以快速重新显示
        isPCKLoaded = false
        showControls = false
        isPresented = false
    }

    private func getFileSize(path: String) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
}

#Preview {
    SimpleGodotGameView(isPresented: .constant(true))
}
