//
//  ContentView.swift
//  focusflyer
//
//  Created by 张国豪 on 2025/7/1.
//

import RiveRuntime
import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct ContentView: View {
    private let riveVM = RiveViewModel(fileName: "spegni_il_cervello")
    @State private var showGodotGame = false

    var body: some View {
        VStack(spacing: 24) {
            RiveViewRepresentable(model: riveVM)
                .aspectRatio(contentMode: .fit)
                .frame(width: 300, height: 300)

            But<PERSON>(action: {
                showGodotGame = true
            }) {
                HStack {
                    Image(systemName: "gamecontroller.fill")
                    Text("Launch Godot Game")
                }
                .font(.title2)
                .foregroundColor(.white)
                .padding()
                .background(Color.blue)
                .cornerRadius(10)
            }
        }
        .padding()
        .fullScreenCover(isPresented: $showGodotGame) {
            SwiftGodotGameView(isPresented: $showGodotGame)
        }
    }
}

#Preview {
  ContentView()
}
