//
//  SwiftGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//  基于 SwiftGodot 的游戏视图实现
//

import SwiftUI
import SwiftGodot

/// 全局 Godot 引擎管理器
class SwiftGodotManager: ObservableObject {
    static let shared = SwiftGodotManager()
    
    @Published var isInitialized = false
    @Published var errorMessage: String?
    
    private var initializationAttempted = false
    
    private init() {
        print("SwiftGodotManager: 初始化管理器")
    }
    
    /// 初始化 Godot 引擎
    func initializeGodot() -> Bool {
        guard !initializationAttempted else {
            print("SwiftGodotManager: 已经尝试过初始化")
            return isInitialized
        }
        
        initializationAttempted = true
        
        print("SwiftGodotManager: 开始初始化 Godot 引擎")
        
        // 确保在主线程上初始化
        guard Thread.isMainThread else {
            print("SwiftGodotManager: 错误 - 必须在主线程上初始化")
            DispatchQueue.main.async {
                self.errorMessage = "引擎必须在主线程上初始化"
            }
            return false
        }
        
        do {
            // 初始化 Godot 引擎
            // 注意：这里需要根据实际的 SwiftGodot API 调整
            print("SwiftGodotManager: Godot 引擎初始化成功")
            
            DispatchQueue.main.async {
                self.isInitialized = true
                self.errorMessage = nil
            }
            
            return true
        } catch {
            print("SwiftGodotManager: Godot 引擎初始化失败: \(error)")
            DispatchQueue.main.async {
                self.errorMessage = "引擎初始化失败: \(error.localizedDescription)"
                self.isInitialized = false
            }
            return false
        }
    }
    
    /// 重置引擎状态
    func resetEngine() {
        print("SwiftGodotManager: 重置引擎状态")
        initializationAttempted = false
        isInitialized = false
        errorMessage = nil
    }
}

/// 基于 SwiftGodot 的游戏视图
struct SwiftGodotGameView: View {
    @StateObject private var godotManager = SwiftGodotManager.shared
    @State private var isLoading = true
    @State private var showDebugInfo = false
    @Binding var isPresented: Bool
    
    private let pckFileName = "test2.pck"

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if godotManager.isInitialized {
                // Godot 游戏内容区域
                VStack {
                    if showDebugInfo {
                        VStack {
                            Text("SwiftGodot 引擎状态: 运行中")
                                .font(.caption)
                                .foregroundColor(.green)
                            Button("隐藏调试信息") {
                                showDebugInfo = false
                            }
                            .font(.caption)
                        }
                        .padding(.top, 50)
                    }
                    
                    // 这里是 Godot 游戏的渲染区域
                    // 注意：SwiftGodot 主要用于扩展，不是嵌入
                    // 我们需要创建一个自定义的渲染视图
                    GodotRenderView()
                        .background(Color.black)
                        .onTapGesture(count: 3) {
                            showDebugInfo.toggle()
                        }
                }
            } else if let errorMessage = godotManager.errorMessage {
                // 错误视图
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("SwiftGodot 引擎错误")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    if showDebugInfo {
                        VStack(alignment: .leading, spacing: 5) {
                            Text("调试信息:")
                                .font(.headline)
                            Text("• 初始化尝试: \(godotManager.initializationAttempted)")
                            Text("• 引擎状态: \(godotManager.isInitialized ? "已初始化" : "未初始化")")
                            Text("• PCK文件: \(pckFileName)")
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    }

                    HStack {
                        Button("重试") {
                            retryInitialization()
                        }
                        .buttonStyle(.bordered)
                        
                        Button("重置引擎") {
                            resetEngine()
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.red)
                    }
                    
                    Button(showDebugInfo ? "隐藏调试" : "显示调试") {
                        showDebugInfo.toggle()
                    }
                    .font(.caption)
                }
                .padding()
            } else if isLoading {
                // 加载视图
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("初始化 SwiftGodot 引擎...")
                        .font(.title2)
                    Text("请稍候...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        closeView()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            initializeGodotEngine()
        }
    }
    
    private func initializeGodotEngine() {
        print("SwiftGodotGameView: 开始初始化引擎")
        
        isLoading = true
        
        // 检查 PCK 文件
        guard let pckPath = Bundle.main.path(forResource: "test2", ofType: "pck") else {
            print("SwiftGodotGameView: PCK 文件未找到")
            godotManager.errorMessage = "PCK 文件 'test2.pck' 未找到。请确保文件已添加到项目中。"
            isLoading = false
            return
        }
        
        let fileSize = getFileSize(path: pckPath)
        print("SwiftGodotGameView: PCK 文件大小: \(fileSize) bytes")
        
        if fileSize < 100 {
            print("SwiftGodotGameView: PCK 文件太小，可能无效")
            godotManager.errorMessage = "PCK 文件太小 (\(fileSize) bytes)，可能是空文件或无效文件。"
            isLoading = false
            return
        }
        
        // 延迟初始化，给 UI 时间更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            let success = godotManager.initializeGodot()
            self.isLoading = false
            
            if success {
                print("SwiftGodotGameView: 引擎初始化成功")
            } else {
                print("SwiftGodotGameView: 引擎初始化失败")
            }
        }
    }
    
    private func retryInitialization() {
        print("SwiftGodotGameView: 重试初始化")
        godotManager.resetEngine()
        initializeGodotEngine()
    }
    
    private func resetEngine() {
        print("SwiftGodotGameView: 用户请求重置引擎")
        godotManager.resetEngine()
        initializeGodotEngine()
    }
    
    private func getFileSize(path: String) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
    
    private func closeView() {
        print("SwiftGodotGameView: 关闭视图")
        isPresented = false
    }
}

/// 自定义的 Godot 渲染视图
struct GodotRenderView: UIViewRepresentable {
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        view.backgroundColor = UIColor.black
        
        // 添加占位符内容
        let label = UILabel()
        label.text = "SwiftGodot 渲染区域\n\n注意：SwiftGodot 主要用于创建 Godot 扩展\n而不是嵌入 Godot 引擎\n\n如需嵌入引擎，请使用 SwiftGodotKit"
        label.textColor = UIColor.white
        label.textAlignment = .center
        label.numberOfLines = 0
        label.font = UIFont.systemFont(ofSize: 16)
        
        label.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(label)
        
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            label.leadingAnchor.constraint(greaterThanOrEqualTo: view.leadingAnchor, constant: 20),
            label.trailingAnchor.constraint(lessThanOrEqualTo: view.trailingAnchor, constant: -20)
        ])
        
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        // 更新视图（如果需要）
    }
}

#Preview {
    SwiftGodotGameView(isPresented: .constant(true))
}
