//
//  SafeGodotGameView.swift
//  focusflyer
//
//  Created by AI Assistant on 2025/7/10.
//  更安全的Godot游戏视图 - 基于官方示例的最简实现
//

import SwiftUI
import SwiftGodotKit
import SwiftGodot

struct SafeGodotGameView: View {
    @State private var godotApp: GodotApp?
    @State private var isLoading = true
    @State private var errorMessage: String?
    @State private var initializationFailed = false
    @Binding var isPresented: Bool
    
    private let pckFileName = "test2.pck"

    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
    }

    var body: some View {
        ZStack {
            if let godotApp = godotApp {
                // Godot游戏视图 - 完全按照官方示例
                GodotAppView()
                    .background(Color.black)
                    .environment(\.godotApp, godotApp)
            } else if let errorMessage = errorMessage {
                // 错误视图
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.red)

                    Text("Godot引擎错误")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(errorMessage)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    VStack(spacing: 10) {
                        Text("可能的解决方案:")
                            .font(.headline)

                        VStack(alignment: .leading, spacing: 5) {
                            Text("1. 确保test2.pck文件存在且有效")
                            Text("2. 重启应用程序")
                            Text("3. 检查Godot项目导出设置")
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)

                    Button("关闭") {
                        isPresented = false
                    }
                    .buttonStyle(.bordered)
                }
                .padding()
            } else if isLoading {
                // 加载视图
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("初始化Godot引擎...")
                        .font(.title2)
                    Text("请稍候...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // 关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: {
                        closeView()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .onAppear {
            initializeGodot()
        }
        .onDisappear {
            // 不在这里清理，让引擎保持运行
        }
    }
    
    private func initializeGodot() {
        print("SafeGodotGameView: 开始初始化Godot")
        
        // 防止重复初始化
        guard godotApp == nil && !initializationFailed else {
            print("SafeGodotGameView: 跳过初始化 - 已存在实例或之前失败")
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        // 检查PCK文件
        guard let pckPath = Bundle.main.path(forResource: "test2", ofType: "pck") else {
            print("SafeGodotGameView: PCK文件未找到")
            showError("PCK文件 'test2.pck' 未找到。请确保文件已添加到项目中。")
            return
        }
        
        let fileSize = getFileSize(path: pckPath)
        print("SafeGodotGameView: PCK文件大小: \(fileSize) bytes")
        
        if fileSize < 100 {
            print("SafeGodotGameView: PCK文件太小，可能无效")
            showError("PCK文件太小 (\(fileSize) bytes)，可能是空文件或无效文件。")
            return
        }
        
        // 在主线程上初始化，但给UI时间更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.createGodotApp()
        }
    }
    
    private func createGodotApp() {
        print("SafeGodotGameView: 创建GodotApp实例")
        
        do {
            // 使用最简单的初始化方式
            let app = GodotApp(packFile: pckFileName)
            
            // 给引擎时间初始化
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.godotApp = app
                self.isLoading = false
                print("SafeGodotGameView: Godot引擎初始化成功")
            }
            
        } catch {
            print("SafeGodotGameView: GodotApp创建失败: \(error)")
            showError("引擎初始化失败: \(error.localizedDescription)")
        }
    }
    
    private func showError(_ message: String) {
        DispatchQueue.main.async {
            self.errorMessage = message
            self.isLoading = false
            self.initializationFailed = true
        }
    }
    
    private func getFileSize(path: String) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
    
    private func closeView() {
        print("SafeGodotGameView: 关闭视图")
        // 简单关闭，不清理引擎
        isPresented = false
    }
}

#Preview {
    SafeGodotGameView(isPresented: .constant(true))
}
