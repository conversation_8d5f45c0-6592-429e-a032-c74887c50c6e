// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		4972CA422DBA8384003ABBBB /* SwiftGodotKit in Frameworks */ = {isa = PBXBuildFile; productRef = 4972CA412DBA8384003ABBBB /* SwiftGodotKit */; };
		4972CA442DBA83DD003ABBBB /* main.pck in Resources */ = {isa = PBXBuildFile; fileRef = 4972CA432DBA83DD003ABBBB /* main.pck */; };
		4972CCD02DBAC79B003ABBBB /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4972CCCF2DBAC79B003ABBBB /* Metal.framework */; };
		4972E1EB2DBD610A003ABBBB /* MetalFX.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4972CCDA2DBACB1E003ABBBB /* MetalFX.framework */; platformFilter = ios; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		4972CA312DBA835D003ABBBB /* SwiftGodotKitSamples.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SwiftGodotKitSamples.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4972CA432DBA83DD003ABBBB /* main.pck */ = {isa = PBXFileReference; lastKnownFileType = file; path = main.pck; sourceTree = "<group>"; };
		4972CCCF2DBAC79B003ABBBB /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.4.sdk/System/Library/Frameworks/Metal.framework; sourceTree = DEVELOPER_DIR; };
		4972CCD12DBAC7BE003ABBBB /* AudioUnit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioUnit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.4.sdk/System/Library/Frameworks/AudioUnit.framework; sourceTree = DEVELOPER_DIR; };
		4972CCDA2DBACB1E003ABBBB /* MetalFX.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalFX.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.4.sdk/System/Library/Frameworks/MetalFX.framework; sourceTree = DEVELOPER_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		4972CA332DBA835D003ABBBB /* SwiftGodotKitSamples */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SwiftGodotKitSamples;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		4972CA2E2DBA835D003ABBBB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4972E1EB2DBD610A003ABBBB /* MetalFX.framework in Frameworks */,
				4972CCD02DBAC79B003ABBBB /* Metal.framework in Frameworks */,
				4972CA422DBA8384003ABBBB /* SwiftGodotKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4972CA282DBA835D003ABBBB = {
			isa = PBXGroup;
			children = (
				4972CA432DBA83DD003ABBBB /* main.pck */,
				4972CA332DBA835D003ABBBB /* SwiftGodotKitSamples */,
				4972CCCE2DBAC79B003ABBBB /* Frameworks */,
				4972CA322DBA835D003ABBBB /* Products */,
			);
			sourceTree = "<group>";
		};
		4972CA322DBA835D003ABBBB /* Products */ = {
			isa = PBXGroup;
			children = (
				4972CA312DBA835D003ABBBB /* SwiftGodotKitSamples.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4972CCCE2DBAC79B003ABBBB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				4972CCDA2DBACB1E003ABBBB /* MetalFX.framework */,
				4972CCD12DBAC7BE003ABBBB /* AudioUnit.framework */,
				4972CCCF2DBAC79B003ABBBB /* Metal.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		4972CA302DBA835D003ABBBB /* SwiftGodotKitSamples */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4972CA3D2DBA835E003ABBBB /* Build configuration list for PBXNativeTarget "SwiftGodotKitSamples" */;
			buildPhases = (
				4972CA2D2DBA835D003ABBBB /* Sources */,
				4972CA2E2DBA835D003ABBBB /* Frameworks */,
				4972CA2F2DBA835D003ABBBB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				4972CA332DBA835D003ABBBB /* SwiftGodotKitSamples */,
			);
			name = SwiftGodotKitSamples;
			packageProductDependencies = (
				4972CA412DBA8384003ABBBB /* SwiftGodotKit */,
			);
			productName = SwiftGodotKitSamples;
			productReference = 4972CA312DBA835D003ABBBB /* SwiftGodotKitSamples.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		4972CA292DBA835D003ABBBB /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					4972CA302DBA835D003ABBBB = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 4972CA2C2DBA835D003ABBBB /* Build configuration list for PBXProject "SwiftGodotKitSamples" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 4972CA282DBA835D003ABBBB;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				4972CA402DBA8384003ABBBB /* XCLocalSwiftPackageReference "../SwiftGodotKit" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 4972CA322DBA835D003ABBBB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4972CA302DBA835D003ABBBB /* SwiftGodotKitSamples */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		4972CA2F2DBA835D003ABBBB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4972CA442DBA83DD003ABBBB /* main.pck in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		4972CA2D2DBA835D003ABBBB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		4972CA3B2DBA835E003ABBBB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = YLLAGX2NRU;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		4972CA3C2DBA835E003ABBBB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = YLLAGX2NRU;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		4972CA3E2DBA835E003ABBBB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SwiftGodotKitSamples/SwiftGodotKitSamples.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = YLLAGX2NRU;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LD_GENERATE_MAP_FILE = YES;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.3;
				MARKETING_VERSION = 1.0;
				MERGED_BINARY_TYPE = none;
				OTHER_LDFLAGS = "-lc++";
				PRODUCT_BUNDLE_IDENTIFIER = org.tirania.SwiftGodotKitSamples;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				SUPPORTS_MACCATALYST = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		4972CA3F2DBA835E003ABBBB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SwiftGodotKitSamples/SwiftGodotKitSamples.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = YLLAGX2NRU;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LD_GENERATE_MAP_FILE = YES;
				LD_MAP_FILE_PATH = /tmp/a.txt;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.3;
				MARKETING_VERSION = 1.0;
				MERGED_BINARY_TYPE = none;
				OTHER_LDFLAGS = "-lc++";
				PRODUCT_BUNDLE_IDENTIFIER = org.tirania.SwiftGodotKitSamples;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				SUPPORTS_MACCATALYST = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4972CA2C2DBA835D003ABBBB /* Build configuration list for PBXProject "SwiftGodotKitSamples" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4972CA3B2DBA835E003ABBBB /* Debug */,
				4972CA3C2DBA835E003ABBBB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4972CA3D2DBA835E003ABBBB /* Build configuration list for PBXNativeTarget "SwiftGodotKitSamples" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4972CA3E2DBA835E003ABBBB /* Debug */,
				4972CA3F2DBA835E003ABBBB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		4972CA402DBA8384003ABBBB /* XCLocalSwiftPackageReference "../SwiftGodotKit" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = ../SwiftGodotKit;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		4972CA412DBA8384003ABBBB /* SwiftGodotKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = SwiftGodotKit;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 4972CA292DBA835D003ABBBB /* Project object */;
}
